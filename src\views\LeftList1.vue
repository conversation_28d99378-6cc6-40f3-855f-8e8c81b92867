<template>
  <div class="LeftList1">
    <div class="list-container enhanced-container">
      <!-- 有数据时显示列表 -->
      <TransitionGroup v-if="sortedListData.length > 0" name="list" tag="div" class="list-items">
        <div
          v-for="(item, index) in sortedListData"
          :key="item.id"
          class="list-item enhanced-list-item"
          :class="`enhanced-list-item-${index + 1}`"
          :style="{ animationDelay: `${parseFloat(props.parentAnimationDelay) + 0.5 + index * 0.1}s` }"
        >
          <span class="item-number enhanced-number" :style="{ '--item-delay': `${parseFloat(props.parentAnimationDelay) + 0.5 + index * 0.1}s` }">No.{{ index + 1 }}</span>
          <span class="item-name enhanced-name" :style="{ '--item-delay': `${parseFloat(props.parentAnimationDelay) + 0.5 + index * 0.1}s` }">{{ item.name }}</span>
          <div class="progress-container enhanced-progress-container" :style="{ '--item-delay': `${parseFloat(props.parentAnimationDelay) + 0.5 + index * 0.1}s` }">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: item.progress + '%' }"></div>
              <div class="progress-indicator" :style="{ left: item.progress + '%' }"></div>
            </div>
          </div>
          <span class="progress-value enhanced-value" :style="{ '--item-delay': `${parseFloat(props.parentAnimationDelay) + 0.5 + index * 0.1}s` }">{{ item.value }}</span>
        </div>
      </TransitionGroup>

      <!-- 无数据时显示暂无数据 -->
      <div v-else class="no-data enhanced-no-data">
        <div class="no-data-text">暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { usedata } from "../store/data";
const dataStore = usedata();

// 接收父组件的动画延迟
const props = defineProps({
  parentAnimationDelay: {
    type: String,
    default: "0s",
  },
});

// 计算属性：从 dataStore 获取动态数据，只取前5条
const listData = computed(() => {
  const attackCountryTop = dataStore.data?.attackCountryTop;
  if (!attackCountryTop || !Array.isArray(attackCountryTop)) {
    // 如果数据还未加载，返回默认值
    return [];
  }

  // 只取前5条数据，并添加ID和进度计算
  const topFive = attackCountryTop.slice(0, 5);
  const maxValue = Math.max(...topFive.map((item) => item.attackNum || 0));

  return topFive.map((item, index) => ({
    id: index + 1,
    name: item.country || `国家${index + 1}`,
    value: item.attackNum || 0,
    progress: maxValue > 0 ? Math.round((item.attackNum / maxValue) * 100) : 0,
  }));
});

// 计算属性：按value值降序排序
const sortedListData = computed(() => {
  return [...listData.value].sort((a, b) => b.value - a.value);
});
</script>

<style lang="less" scoped>
.LeftList1 {
  width: 100%;
  height: 100%;
  padding: 20px;
  color: #fff;
  font-family: "Microsoft YaHei", sans-serif;
}

.list-container {
  width: 100%;
  height: 100%;
}

.list-items {
  width: 100%;
}

.list-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 15px;

  &:last-child {
    margin-bottom: 0;
  }
}

.item-number {
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.item-name {
  color: #a0a0a0;
  font-weight: 400;
  font-size: 13px;
  flex-shrink: 0;
}

.progress-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.progress-indicator {
  position: absolute;
  top: 50%;
  width: 18px;
  height: 18px;
  background-image: url("../../public/img/e.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: rgba(0, 247, 255, 0.205);
  border-radius: 2px;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #707070 0%, #00bfff 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-value {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  text-align: right;
  flex-shrink: 0;
}

// 增强的容器动画
.enhanced-container {
  animation: containerFadeIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: 0.3s; /* 在父容器滑入后开始 */
  opacity: 0;
  transform: translateY(20px);
}

@keyframes containerFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 增强的列表项动画
.enhanced-list-item {
  perspective: 1000px;
  transform-style: preserve-3d;
  animation: enhanced3DSlideIn 800ms cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
  transform: translateX(-150px) rotateY(-25deg) scale(0.6);
  opacity: 0;
  filter: drop-shadow(0 5px 15px rgba(50, 254, 252, 0.2));
  transition: all 0.3s ease;

  &:hover {
    transform: translateX(5px) rotateY(2deg) scale(1.02);
    filter: drop-shadow(0 8px 25px rgba(50, 254, 252, 0.4)) brightness(1.1);
    background: linear-gradient(90deg, rgba(50, 254, 252, 0.05) 0%, rgba(50, 254, 252, 0.1) 100%);
    border-radius: 8px;
  }
}

@keyframes enhanced3DSlideIn {
  0% {
    transform: translateX(-150px) rotateY(-25deg) scale(0.6);
    opacity: 0;
    filter: drop-shadow(0 2px 8px rgba(50, 254, 252, 0.1));
  }
  30% {
    transform: translateX(-50px) rotateY(-10deg) scale(0.8);
    opacity: 0.5;
    filter: drop-shadow(0 4px 12px rgba(50, 254, 252, 0.2));
  }
  70% {
    transform: translateX(10px) rotateY(3deg) scale(1.05);
    opacity: 0.9;
    filter: drop-shadow(0 6px 18px rgba(50, 254, 252, 0.4));
  }
  100% {
    transform: translateX(0) rotateY(0deg) scale(1);
    opacity: 1;
    filter: drop-shadow(0 5px 15px rgba(50, 254, 252, 0.2));
  }
}

// 列表动画效果 - 保持原有的TransitionGroup动画
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}

.list-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.list-move {
  transition: transform 0.5s ease;
}

// 增强的数字动画
.enhanced-number {
  animation: numberGlow 900ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: calc(var(--item-delay, 0s) + 0.1s);
  opacity: 0;
  transform: scale(0.3) rotateZ(-20deg);
  display: inline-block;

  &:hover {
    color: #32fefc;
    text-shadow: 0 0 10px rgba(50, 254, 252, 0.8);
    transform: scale(1.1);
  }
}

@keyframes numberGlow {
  0% {
    opacity: 0;
    transform: scale(0.3) rotateZ(-20deg);
    color: #666;
  }
  40% {
    opacity: 0.7;
    transform: scale(1.3) rotateZ(5deg);
    color: #32fefc;
    text-shadow: 0 0 20px rgba(50, 254, 252, 0.8);
  }
  80% {
    opacity: 0.95;
    transform: scale(0.9) rotateZ(-2deg);
    color: #32fefc;
    text-shadow: 0 0 10px rgba(50, 254, 252, 0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotateZ(0deg);
    color: white;
    text-shadow: 0 0 5px rgba(50, 254, 252, 0.3);
  }
}

// 增强的名称动画
.enhanced-name {
  animation: nameSlideIn 800ms cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
  animation-delay: calc(var(--item-delay, 0s) + 0.2s);
  opacity: 0;
  transform: translateX(-40px) scale(0.8);

  &:hover {
    color: #32fefc;
    text-shadow: 0 0 8px rgba(50, 254, 252, 0.5);
  }
}

@keyframes nameSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-40px) scale(0.8);
  }
  60% {
    opacity: 0.8;
    transform: translateX(5px) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

// 增强的进度条容器动画 - 简化版本，只保留进入效果
.enhanced-progress-container {
  animation: progressContainerSlideIn 800ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: calc(var(--item-delay, 0s) + 0.2s);
  opacity: 0;
  transform: translateX(-20px) scale(0.9);
}

@keyframes progressContainerSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-20px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

// 增强的数值动画
.enhanced-value {
  animation: valueCountUp 1200ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: calc(var(--item-delay, 0s) + 0.5s);
  opacity: 0;
  transform: scale(0.3) rotateZ(10deg);

  &:hover {
    color: #32fefc;
    text-shadow: 0 0 10px rgba(50, 254, 252, 0.8);
    transform: scale(1.1);
  }
}

@keyframes valueCountUp {
  0% {
    opacity: 0;
    transform: scale(0.3) rotateZ(10deg);
  }
  30% {
    opacity: 0.6;
    transform: scale(1.4) rotateZ(-5deg);
    color: #32fefc;
    text-shadow: 0 0 20px rgba(50, 254, 252, 0.8);
  }
  70% {
    opacity: 0.9;
    transform: scale(0.9) rotateZ(2deg);
    color: #32fefc;
    text-shadow: 0 0 12px rgba(50, 254, 252, 0.6);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotateZ(0deg);
    color: #ffffff;
  }
}

// 数值变化动画 - 优化为实时更新
.progress-fill {
  transition: width 0.1s ease-out;
}

.progress-indicator {
  transition: left 0.1s ease-out;
}

.progress-value {
  transition: all 0.1s ease-out;
  font-variant-numeric: tabular-nums; // 数字等宽，避免跳动
}

// 增强的无数据状态动画
.enhanced-no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
  font-size: 14px;
  animation: noDataFadeIn 800ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  opacity: 0;
  transform: translateY(20px);

  .no-data-text {
    color: #999;
    font-size: 16px;
    font-weight: 400;
    animation: textPulse 2s ease-in-out infinite;
  }
}

@keyframes noDataFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes textPulse {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
    color: #32fefc;
    text-shadow: 0 0 10px rgba(50, 254, 252, 0.3);
  }
}

// 暂无数据样式 - 保持原有样式作为备用
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
  font-size: 14px;
}

.no-data-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.no-data-text {
  color: #999;
  font-size: 16px;
  font-weight: 400;
}
</style>
